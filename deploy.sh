PART=$1
CMD=$2
CONFIG=$3
CKPT=$4
ARGS=${@:5}

PILLAR_HOME="$(dirname $0)/../../.."
export PYTHONPATH=$PYTHONPATH:$(dirname $0)/..:$PILLAR_HOME:$PILLAR_HOME/functions

CONFIG_PATH=${CONFIG#*functions/}
FUNCTION_NAME=${CONFIG_PATH%%/*}
CONFIG_FILE=${CONFIG##*/}
CONFIG_STEM=${CONFIG_FILE%.*}
export WORK_DIR=$PILLAR_HOME/work_dirs/$FUNCTION_NAME/$CONFIG_STEM/deploy
mkdir -p $WORK_DIR

srun -p $PART --quotatype=auto -N1 --gres=gpu:1 \
python -m deploy $CMD $CONFIG "$CKPT" -o $WORK_DIR $ARGS \
2>&1 | tee $WORK_DIR/$CMD.log
